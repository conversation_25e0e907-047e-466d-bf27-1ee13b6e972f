<div>
    <x-form.input.text label="Phone" labelRequired="0" model="phone" type="tel" placeholder="Enter phone number" />
    <x-form.input.text label="Fax" labelRequired="0" model="fax" type="tel" placeholder="Enter fax number" />
    <x-form.input.drop-down label="Dispatch Method" labelRequired="1" model="dispatch_method">
        <option value="">Select Method</option>
        <option value="Fax" {{ $dispatch_method == 'Fax' ? 'selected' : '' }}>Fax</option>
        <option value="Dispense Pro" {{ $dispatch_method == 'Dispense Pro' ? 'selected' : '' }}>Dispense Pro</option>
    </x-form.input.drop-down>

    <x-form.input.text label="Address" labelRequired="1" model="address" placeholder="Enter street address" />
    <x-form.input.text label="City" labelRequired="1" model="city" placeholder="Enter city" />
    <x-form.input.drop-down label="State" labelRequired="1" model="state" placeholder="Select State">
        <option value="">Select State</option>
        @foreach ($states as $stateOption)
            <option value="{{ $stateOption->abbreviation }}" {{ $state == $stateOption->abbreviation ? 'selected' : '' }}>
                {{ $stateOption->name }}
            </option>
        @endforeach
    </x-form.input.drop-down>
    <x-form.input.text label="ZIP Code" labelRequired="1" model="zip" placeholder="Enter ZIP code (12345 or 12345-6789)" />
</div>
