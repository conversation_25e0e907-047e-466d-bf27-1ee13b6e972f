<div>
    <!-- Step 1: Select Patients Provider -->
    <div class="row">
        <div class="col-12">
            <h5 class="mb-4">Select Patient's Provider</h5>
            <p class="text-muted mb-4">Choose the healthcare provider who will be responsible for this patient.</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <x-form.input.drop-down
                label="Healthcare Provider"
                labelRequired="1"
                model="provider_id"
                placeholder="Select a provider"
            >
                <option value="">Select Provider</option>
                <option value="1">Dr. <PERSON> - Internal Medicine</option>
                <option value="2">Dr. <PERSON> - Family Practice</option>
                <option value="3">Dr. <PERSON> - <PERSON></option>
                <option value="4">Dr. <PERSON> - <PERSON>ediatric<PERSON></option>
                <option value="5">Dr. <PERSON> - <PERSON>thopedics</option>
            </x-form.input.drop-down>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-light border">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <strong>Note:</strong> The selected provider will have access to this patient's medical records and will be responsible for their care coordination.
            </div>
        </div>
    </div>
</div>
