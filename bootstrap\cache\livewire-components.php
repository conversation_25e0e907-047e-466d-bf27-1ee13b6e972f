<?php return array (
  'import.view' => 'App\\Http\\Livewire\\Import\\View',
  'medication.create-edit' => 'App\\Http\\Livewire\\Medication\\CreateEdit',
  'new-import' => 'App\\Http\\Livewire\\NewImport',
  'patient.create-edit' => 'App\\Http\\Livewire\\Patient\\CreateEdit',
  'practice.create-edit' => 'App\\Http\\Livewire\\Practice\\CreateEdit',
  'practice.medications.create-edit' => 'App\\Http\\Livewire\\Practice\\Medications\\CreateEdit',
  'practice.provider.create-edit' => 'App\\Http\\Livewire\\Practice\\Provider\\CreateEdit',
  'provider.provider-form' => 'App\\Http\\Livewire\\Provider\\ProviderForm',
  'scripts-panel' => 'App\\Http\\Livewire\\ScriptsPanel',
  'settings.about' => 'App\\Http\\Livewire\\Settings\\About',
  'settings.change-password' => 'App\\Http\\Livewire\\Settings\\ChangePassword',
  'settings.fax-options' => 'App\\Http\\Livewire\\Settings\\FaxOptions',
  'settings.privacy' => 'App\\Http\\Livewire\\Settings\\Privacy',
  'settings.terms' => 'App\\Http\\Livewire\\Settings\\Terms',
  'shared.multi-step-form' => 'App\\Http\\Livewire\\Shared\\MultiStepForm',
  'template.create-edit' => 'App\\Http\\Livewire\\Template\\CreateEdit',
  'user.create-edit' => 'App\\Http\\Livewire\\User\\CreateEdit',
  'user.user-admin' => 'App\\Http\\Livewire\\User\\UserAdmin',
);