<div>
    <?php
        use App\Models\User;
        $user = auth()->user();
    ?>
    <form wire:submit.prevent="store">
        <?php if (isset($component)) { $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.row','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.row'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
            <div class="col">
                <?php if (isset($component)) { $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.body','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('card.body'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <?php if (isset($component)) { $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.row','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.row'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                        <?php if($user->role === User::ROLE_ADMIN || $user->role === User::ROLE_OPERATOR): ?>
                            <?php if (isset($component)) { $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.col','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.col'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                                <div wire:ignore class="col" style="padding: 0;">
                                    <label for="provider_id">Provider <span class="text-danger">*</span></label>
                                    <select wire:model="patient.provider_id" id="provider_id" class="form-control">
                                        <option value="">Select Provider</option>
                                        <?php $__currentLoopData = $providers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $provider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($provider->id); ?>"><?php echo e($provider->first_name); ?>

                                                <?php echo e($provider->last_name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <?php if (isset($component)) { $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.error','data' => ['model' => 'patient.provider_id']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['model' => 'patient.provider_id']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $attributes = $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $component = $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $attributes = $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $component = $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>
                        <?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.col','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.col'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                            <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'First Name','labelRequired' => '1','model' => 'patient.first_name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'First Name','labelRequired' => '1','model' => 'patient.first_name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $attributes = $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $component = $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.col','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.col'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                            <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Last Name','labelRequired' => '1','model' => 'patient.last_name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Last Name','labelRequired' => '1','model' => 'patient.last_name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $attributes = $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $component = $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.col','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.col'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                            <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'DOB','labelRequired' => '1','model' => 'patient.dob','id' => 'dob_date_input','placeholder' => 'mm/dd/yyyy','readonly' => 'readonly']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'DOB','labelRequired' => '1','model' => 'patient.dob','id' => 'dob_date_input','placeholder' => 'mm/dd/yyyy','readonly' => 'readonly']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $attributes = $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $component = $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.col','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.col'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                            <div class="form-group" wire:ignore style="margin-bottom: 0">
                                <label for="gender" class="form-label">Gender</label><span
                                    class="text-danger">*</span>
                                <select wire:model="patient.gender" id="gender" class="form-control gender-select">
                                    <option value="">Select Gender</option>
                                    <option value="M">Male</option>
                                    <option value="F">Female</option>
                                </select>
                            </div>
                            <?php if (isset($component)) { $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.error','data' => ['model' => 'patient.gender']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['model' => 'patient.gender']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $attributes = $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $component = $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $attributes = $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $component = $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.col','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.col'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                            <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Address','labelRequired' => '1','model' => 'patient.address']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Address','labelRequired' => '1','model' => 'patient.address']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $attributes = $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $component = $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.col','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.col'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                            <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'City','labelRequired' => '1','model' => 'patient.city']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'City','labelRequired' => '1','model' => 'patient.city']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $attributes = $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $component = $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.col','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.col'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                            <div class="form-group" wire:ignore style="margin-bottom: 0">
                                <label for="state" class="form-label">State</label> <span
                                    class="text-danger">*</span>
                                <select wire:model="patient.state_id" id="state" class="form-control gender-select">
                                    <option value="">Select State</option>
                                    <?php $__currentLoopData = $states; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $state): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($state->id); ?>"><?php echo e($state->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <?php if (isset($component)) { $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.error','data' => ['model' => 'patient.state_id']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['model' => 'patient.state_id']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $attributes = $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $component = $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $attributes = $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $component = $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.col','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.col'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                            <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Zip Code','labelRequired' => '1','model' => 'patient.zip_code']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Zip Code','labelRequired' => '1','model' => 'patient.zip_code']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $attributes = $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $component = $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.col','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.col'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                            <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Phone','model' => 'patient.phone_number','type' => 'number']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Phone','model' => 'patient.phone_number','type' => 'number']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $attributes = $__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__attributesOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79)): ?>
<?php $component = $__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79; ?>
<?php unset($__componentOriginal3c04de9f3b0a424b3f1ed713eed48b79); ?>
<?php endif; ?>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $attributes = $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $component = $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $attributes = $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $component = $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.footer','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('card.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <?php if (isset($component)) { $__componentOriginald2555272519db269fb0587805fbfa3da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald2555272519db269fb0587805fbfa3da = $attributes; } ?>
<?php $component = App\View\Components\BtnWithLoading::resolve(['target' => 'store','text' => 'Save','type' => 'submit'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('btn-with-loading'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\BtnWithLoading::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald2555272519db269fb0587805fbfa3da)): ?>
<?php $attributes = $__attributesOriginald2555272519db269fb0587805fbfa3da; ?>
<?php unset($__attributesOriginald2555272519db269fb0587805fbfa3da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald2555272519db269fb0587805fbfa3da)): ?>
<?php $component = $__componentOriginald2555272519db269fb0587805fbfa3da; ?>
<?php unset($__componentOriginald2555272519db269fb0587805fbfa3da); ?>
<?php endif; ?>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0)): ?>
<?php $attributes = $__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0; ?>
<?php unset($__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0)): ?>
<?php $component = $__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0; ?>
<?php unset($__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0); ?>
<?php endif; ?>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $attributes = $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $component = $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
    </form>

    <?php if(session()->has('error-message')): ?>
        <div class="text-danger p-5 mb-3" role="alert">
            <?php echo e(session('error-message')); ?>

            <button type="button" class="close text-danger" onclick="this.parentElement.remove()" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>
</div>

<?php $__env->startPush('styles'); ?>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<?php $__env->stopPush(); ?>
<?php $__env->startPush('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        document.addEventListener("livewire:load", function() {
            function createRoleDropdown() {
                $('#user\\.role').select2({
                    placeholder: "Select Access Type",
                    minimumResultsForSearch: Infinity, // Disable the search functionality
                }).on('change', function(e) {
                    window.livewire.find('<?php echo e($_instance->id); ?>').set('user.role', $(e.target).val());
                });
            }

            createRoleDropdown();
            initializeFlatpickrDobPicker();

            // Re-initialize Select2 after Livewire updates the DOM
            window.livewire.on('contentChanged', function() {
                createRoleDropdown();
            });
        });

        function initializeFlatpickrDobPicker() {
            const dobInput = document.getElementById('dob_date_input');
            if (!dobInput) return;

            const rawValue = dobInput.value;

            flatpickr(dobInput, {
                dateFormat: "m/d/Y",
                allowInput: true,
                clickOpens: true,
                maxDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
                defaultDate: rawValue ? (() => {
                    const [year, month, day] = rawValue.split("-");
                    return `${month}/${day}/${year}`;
                })() : undefined,
                onReady: function(selectedDates, dateStr, instance) {
                    if (!rawValue) {
                        instance.currentYear = 1990;
                        instance.currentMonth = 0; // January
                        instance.redraw();
                    }
                }
            });
        }

        $(document).ready(function() {
            $('#state').select2({
                placeholder: "Select State",
            }).on('change', function(e) {
                // Clear validation error immediately when user selects an option
                $(e.target).removeClass('is-invalid');
                $(e.target).closest('.form-group').find('.form-text.text-danger').remove();
                window.livewire.find('<?php echo e($_instance->id); ?>').set('patient.state_id', $(e.target).val());
            });

            $('#provider_id').select2({
                placeholder: "Select Provider",
            }).on('change', function(e) {
                // Clear validation error immediately when user selects an option
                $(e.target).removeClass('is-invalid');
                $(e.target).closest('.col').find('.form-text.text-danger').remove();
                window.livewire.find('<?php echo e($_instance->id); ?>').set('patient.provider_id', $(e.target).val());
            });

            $('#gender').select2({
                placeholder: "Select Gender",
            }).on('change', function(e) {
                // Clear validation error immediately when user selects an option
                $(e.target).removeClass('is-invalid');
                $(e.target).closest('.form-group').find('.form-text.text-danger').remove();
                window.livewire.find('<?php echo e($_instance->id); ?>').set('patient.gender', $(e.target).val());
            });

            // Clear validation errors for regular input fields when user starts typing
            $('input[wire\\:model]').on('input', function() {
                $(this).removeClass('is-invalid');
                $(this).siblings('.form-text.text-danger').remove();
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/livewire/patient/create-edit.blade.php ENDPATH**/ ?>