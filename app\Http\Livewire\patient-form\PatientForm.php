<?php

namespace App\Http\Livewire\PatientForm;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Log;

class PatientForm extends Component
{
    use WithFileUploads;

    protected $listeners = ['loadDemoData'];

    public $step = 1;
    public $editMode = false;
    public $model;

    // Step 1: Provider Selection
    public $provider_id;

    // Step 2: Basic Patient Details
    public $first_name, $last_name, $dob, $gender, $phone_number, $email;

    // Step 3: Residential Details
    public $address, $city, $state_id, $zip_code, $emergency_contact_name, $emergency_contact_phone;

    // Step 4: Additional Information
    public $insurance_provider, $insurance_policy_number, $medical_conditions, $medications, $allergies;

    // Dynamic steps configuration
    public $steps = [];
    public $totalSteps = 4;

    public function mount($model = null, $editMode = false, $steps = null)
    {
        $this->editMode = $editMode;
        $this->initializeSteps($steps);

        if ($this->editMode && $model) {
            $this->model = $model;
            $this->loadModelData();
        }
    }

    public function initializeSteps($customSteps = null)
    {
        if ($customSteps) {
            $this->steps = $customSteps;
            $this->totalSteps = count($customSteps);
        } else {
            // Default steps
            $this->steps = [
                1 => 'Select Patients Provider',
                2 => 'Patient Basic Detail',
                3 => 'Patient Resedential Detail',
                4 => 'Patient done'
            ];
            $this->totalSteps = 4;
        }
    }

    public function loadModelData()
    {
        // Load model data into form fields
        if ($this->model) {
            $this->provider_id = $this->model->provider_id;
            $this->first_name = $this->model->first_name;
            $this->last_name = $this->model->last_name;
            $this->dob = $this->model->dob;
            $this->gender = $this->model->gender;
            $this->phone_number = $this->model->phone_number;
            $this->email = $this->model->email;
            $this->address = $this->model->address;
            $this->city = $this->model->city;
            $this->state_id = $this->model->state_id;
            $this->zip_code = $this->model->zip_code;
            $this->emergency_contact_name = $this->model->emergency_contact_name;
            $this->emergency_contact_phone = $this->model->emergency_contact_phone;
            $this->insurance_provider = $this->model->insurance_provider;
            $this->insurance_policy_number = $this->model->insurance_policy_number;
            $this->medical_conditions = $this->model->medical_conditions;
            $this->medications = $this->model->medications;
            $this->allergies = $this->model->allergies;
        }
    }

    public function loadDemoData()
    {
        // Load demo data for testing
        $this->provider_id = '2';
        $this->first_name = 'John';
        $this->last_name = 'Doe';
        $this->dob = '1985-06-15';
        $this->gender = 'male';
        $this->phone_number = '(*************';
        $this->email = '<EMAIL>';
        $this->address = '123 Main Street, Apt 4B';
        $this->city = 'Los Angeles';
        $this->state_id = '1';
        $this->zip_code = '90210';
        $this->emergency_contact_name = 'Jane Doe';
        $this->emergency_contact_phone = '(*************';
        $this->insurance_provider = 'Blue Cross Blue Shield';
        $this->insurance_policy_number = 'BC123456789';
        $this->medical_conditions = 'Hypertension, Type 2 Diabetes';
        $this->medications = 'Metformin 500mg twice daily, Lisinopril 10mg once daily';
        $this->allergies = 'Penicillin, Shellfish';
    }

    public function render()
    {
        return view('livewire.patient-form.patient-form');
    }

    public function nextStep()
    {
        $this->validateCurrentStep();
        if ($this->step < $this->totalSteps) {
            $this->step++;
        }
    }

    public function prevStep()
    {
        if ($this->step > 1) {
            $this->step--;
        }
    }

    public function goToStep($stepNumber)
    {
        // Only allow direct step navigation in edit mode
        if ($this->editMode && $stepNumber >= 1 && $stepNumber <= $this->totalSteps) {
            $this->step = $stepNumber;
        }
    }

    private function validateCurrentStep()
    {
        $rules = [];

        switch ($this->step) {
            case 1:
                $rules = [
                    'provider_id' => 'required|exists:users,id',
                ];
                break;

            case 2:
                $rules = [
                    'first_name' => 'required|string|max:255',
                    'last_name' => 'required|string|max:255',
                    'dob' => 'required|date|before:today',
                    'gender' => 'required|in:male,female,other',
                    'phone_number' => 'required|string|max:20',
                    'email' => 'nullable|email|max:255',
                ];
                break;

            case 3:
                $rules = [
                    'address' => 'required|string|max:500',
                    'city' => 'required|string|max:255',
                    'state_id' => 'required|exists:states,id',
                    'zip_code' => 'required|string|max:10',
                    'emergency_contact_name' => 'required|string|max:255',
                    'emergency_contact_phone' => 'required|string|max:20',
                ];
                break;

            case 4:
                $rules = [
                    'insurance_provider' => 'nullable|string|max:255',
                    'insurance_policy_number' => 'nullable|string|max:255',
                    'medical_conditions' => 'nullable|string|max:1000',
                    'medications' => 'nullable|string|max:1000',
                    'allergies' => 'nullable|string|max:1000',
                ];
                break;
        }

        if (!empty($rules)) {
            $this->validate($rules);
        }
    }

    public function submit()
    {
        $this->validateCurrentStep();

        try {
            if ($this->editMode) {
                $this->updateModel();
            } else {
                $this->createModel();
            }

            session()->flash('success-message', $this->editMode ? 'Record updated successfully!' : 'Record created successfully!');
            // return redirect()->route('your.index.route');
        } catch (\Exception $e) {
            Log::error('Form save error: ' . $e->getMessage());
            session()->flash('error-message', 'An error occurred while saving. Please try again.');
        }
    }

    private function createModel()
    {
        // Implement model creation logic
        // Example:
        // YourModel::create([
        //     'field1' => $this->field1,
        //     'field2' => $this->field2,
        // ]);
    }

    private function updateModel()
    {
        // Implement model update logic
        // Example:
        // $this->model->update([
        //     'field1' => $this->field1,
        //     'field2' => $this->field2,
        // ]);
    }
}
