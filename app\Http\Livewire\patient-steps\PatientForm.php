<?php

namespace App\Http\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Log;

class PatientForm extends Component
{
    use WithFileUploads;

    public $step = 1;
    public $editMode = false;
    public $model;

    // Add your form fields here
    // public $field1, $field2, $field3;

    // Dynamic steps configuration
    public $steps = [];
    public $totalSteps = 3;

    public function mount($model = null, $editMode = false, $steps = null)
    {
        $this->editMode = $editMode;
        $this->initializeSteps($steps);

        if ($this->editMode && $model) {
            $this->model = $model;
            $this->loadModelData();
        }
    }

    public function initializeSteps($customSteps = null)
    {
        if ($customSteps) {
            $this->steps = $customSteps;
            $this->totalSteps = count($customSteps);
        } else {
            // Default steps
            $this->steps = [
                1 => 'select-provider',
                2 => 'basic-detail',
                3 => 'address-detail'
            ];
            $this->totalSteps = 3;
        }
    }

    public function loadModelData()
    {
        // Load model data into form fields
        // Example:
        // $this->field1 = $this->model->field1;
        // $this->field2 = $this->model->field2;
    }

    public function render()
    {
        return view('livewire.patient-steps.patient-form');
    }

    public function nextStep()
    {
        $this->validateCurrentStep();
        if ($this->step < $this->totalSteps) {
            $this->step++;
        }
    }

    public function prevStep()
    {
        if ($this->step > 1) {
            $this->step--;
        }
    }

    public function goToStep($stepNumber)
    {
        // Only allow direct step navigation in edit mode
        if ($this->editMode && $stepNumber >= 1 && $stepNumber <= $this->totalSteps) {
            $this->step = $stepNumber;
        }
    }

    private function validateCurrentStep()
    {
        $rules = [];

        switch ($this->step) {
            case 1:
                $rules = [
                    // Add validation rules for step 1
                ];
                break;

            case 2:
                $rules = [
                    // Add validation rules for step 2
                ];
                break;

            case 3:
                $rules = [
                    // Add validation rules for step 3
                ];
                break;
        }

        if (!empty($rules)) {
            $this->validate($rules);
        }
    }

    public function submit()
    {
        $this->validateCurrentStep();

        try {
            if ($this->editMode) {
                $this->updateModel();
            } else {
                $this->createModel();
            }

            session()->flash('success-message', $this->editMode ? 'Record updated successfully!' : 'Record created successfully!');
            // return redirect()->route('your.index.route');
        } catch (\Exception $e) {
            Log::error('Form save error: ' . $e->getMessage());
            session()->flash('error-message', 'An error occurred while saving. Please try again.');
        }
    }

    private function createModel()
    {
        // Implement model creation logic
        // Example:
        // YourModel::create([
        //     'field1' => $this->field1,
        //     'field2' => $this->field2,
        // ]);
    }

    private function updateModel()
    {
        // Implement model update logic
        // Example:
        // $this->model->update([
        //     'field1' => $this->field1,
        //     'field2' => $this->field2,
        // ]);
    }
}
