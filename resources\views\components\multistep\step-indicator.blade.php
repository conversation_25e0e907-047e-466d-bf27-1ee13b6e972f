@props([
    'steps' => [],
    'currentStep' => 1,
    'editMode' => false,
    'gap' => '4rem'
])

<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-center" style="gap: {{ $gap }};">
            @foreach($steps as $stepNumber => $stepLabel)
                <div class="text-center">
                    @if($editMode)
                        <button
                            type="button"
                            wire:click="goToStep({{ $stepNumber }})"
                            class="btn p-0 border-0"
                            style="background: none;"
                        >
                            <div class="d-flex flex-column align-items-center">
                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px; background-color: {{ $currentStep === $stepNumber ? '#e9c5ff' : '#d1d5db' }}; color: {{ $currentStep === $stepNumber ? '#7c3aed' : '#6b7280' }}; font-weight: 600; font-size: 1.1rem;">
                                    {{ $stepNumber }}
                                </div>
                                <div class="mt-2" style="font-size: 0.875rem; color: {{ $currentStep === $stepNumber ? '#7c3aed' : '#6b7280' }}; white-space: nowrap;">
                                    {{ $stepLabel }}
                                </div>
                            </div>
                        </button>
                    @else
                        <div class="d-flex flex-column align-items-center">
                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                 style="width: 50px; height: 50px; background-color: {{ $currentStep === $stepNumber ? '#e9c5ff' : '#d1d5db' }}; color: {{ $currentStep === $stepNumber ? '#7c3aed' : '#6b7280' }}; font-weight: 600; font-size: 1.1rem;">
                                {{ $stepNumber }}
                            </div>
                            <div class="mt-2" style="font-size: 0.875rem; color: {{ $currentStep === $stepNumber ? '#7c3aed' : '#6b7280' }}; white-space: nowrap;">
                                {{ $stepLabel }}
                            </div>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    </div>
</div>
