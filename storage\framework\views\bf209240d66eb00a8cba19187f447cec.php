<!DOCTYPE html>

<html lang="en">

<head>
    <title><?php echo e(config('app.name')); ?> | Login</title>
    <meta name="description" content="Login page" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700" />
    <link href="<?php echo e(asset('css/pages/login/classic/login-4.css')); ?>" rel="stylesheet" type="text/css" />

    <link href="<?php echo e(asset('css/style.bundle.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('css/common.css')); ?>" rel="stylesheet" type="text/css" />

    <link rel="shortcut icon" href="<?php echo e(asset('images/logo.png')); ?>" />
    <style>
        /* .login-bg{
            background: url('https://source.unsplash.com/aEnH4hJ_Mrs/1920x1080');
            background-repeat: no-repeat;
            background-size: cover;
        } */
    </style>
</head>

<body id="kt_body"
    class="header-fixed header-mobile-fixed subheader-enabled subheader-fixed aside-enabled aside-fixed aside-minimize-hoverable page-loading">

    <div class="d-flex flex-column flex-root">

        <div class="login login-4 login-signin-on d-flex flex-row-fluid" id="kt_login">
            <div class="d-flex flex-center flex-row-fluid bgi-size-cover bgi-position-top bgi-no-repeat login-bg">
                
                <div class="login-form text-center p-7 position-relative overflow-hidden">
                    <!--begin::Login Header-->

                    <!--end::Login Header-->
                    <!--begin::Login Sign in form-->
                    <div class="card card-body">
                        <form class="form" method="POST" action="<?php echo e(route('login')); ?>" id="kt_login_signin_form">
                            <?php echo csrf_field(); ?>

                            <div class="d-flex flex-center mb-8">
                                <img width="100px" src="<?php echo e(asset('img/newliferx-logo.svg')); ?>"
                                    alt="<?php echo e(config('app.name')); ?> Logo">
                                
                            </div>

                            <div class="login-signin">
                                
                                <?php if(is_on_staff_portal()): ?>
                                    <h3 class="font-weight-bolder mb-6">Staff Portal</h3>
                                <?php else: ?>
                                    <h3 class="font-weight-bolder mb-6">Provider Portal</h3>
                                <?php endif; ?>
                                <div class="form-group mb-5">

                                    <input
                                        class="form-control h-auto form-control-solid py-4 px-8 <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        required type="email" placeholder="Email" name="email" />
                                </div>
                                <div class="form-group mb-5">
                                    <input
                                        class="form-control h-auto form-control-solid py-4 px-8 <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        required type="password" placeholder="Password" name="password"
                                        autocomplete="current-password" />
                                </div>
                                <div class="form-group d-flex flex-wrap justify-content-between align-items-center">
                                    <div class="checkbox-inline">
                                        <label class="checkbox m-0 text-muted">
                                            <input type="checkbox" name="remember"
                                                <?php echo e(old('remember') ? 'checked' : ''); ?> />
                                            <span></span>Remember me</label>
                                    </div>
                                    <a href="<?php echo e(route('forgot-password')); ?>" class="text-primary">Forget Password ?</a>
                                </div>
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="fv-plugins-message-container">
                                        <span class="fv-help-block" role="alert">
                                            <strong><?php echo e($error); ?></strong>
                                        </span>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <button id="kt_login_signin_submit"
                                    class="btn btn-primary font-weight-bold px-9 py-4 my-3 mx-4">Sign In</button>
                            </div>
                        </form>
                        
                    </div>
                    <!--end::Login Sign in form-->
                </div>
            </div>
        </div>
        <!--end::Login-->
    </div>

    
    
</body>

</html>
<?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/auth/login.blade.php ENDPATH**/ ?>