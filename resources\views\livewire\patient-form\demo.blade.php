@extends('layouts.app')

@section('title', $title ?? 'Patient Registration Form')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Patient Registration Form</h1>
                    <p class="text-muted">Multi-step form demo with reusable components</p>
                </div>
                <div>
                    <button onclick="Livewire.emit('loadDemoData')" class="btn btn-info me-2">
                        <i class="fas fa-magic me-1"></i> Load Demo Data
                    </button>
                    <a href="{{ route('patients.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Patients
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-12 col-lg-10 col-xl-8">
            @livewire('patient-form.patient-form')
        </div>
    </div>
</div>

@push('styles')
<style>
    .container-fluid {
        background-color: #f8f9fa;
        min-height: 100vh;
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
    
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .form-control:focus {
        border-color: #7c3aed;
        box-shadow: 0 0 0 0.2rem rgba(124, 58, 237, 0.25);
    }
    
    .btn-primary {
        background-color: #7c3aed;
        border-color: #7c3aed;
    }
    
    .btn-primary:hover {
        background-color: #6d28d9;
        border-color: #6d28d9;
    }
    
    .text-primary {
        color: #7c3aed !important;
    }
    
    .alert-light {
        background-color: #f8f9fa;
        border-color: #dee2e6;
    }
    
    .alert-success {
        background-color: #d1f2eb;
        border-color: #a7f3d0;
        color: #065f46;
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize any additional JavaScript if needed
        console.log('Patient form demo loaded');
        
        // Add some demo data for testing (optional)
        window.addEventListener('livewire:load', function () {
            // You can add demo data here if needed
            console.log('Livewire component loaded');
        });
    });
</script>
@endpush
@endsection
