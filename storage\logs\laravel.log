[2025-07-31 09:32:21] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:02:21 +0630","user_id":1} 
[2025-07-31 09:32:24] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:02:24 +0630","user_id":1} 
[2025-07-31 09:32:26] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:02:26 +0630","user_id":1} 
[2025-07-31 09:32:40] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:02:39 +0630","user_id":1} 
[2025-07-31 09:34:00] local.INFO: Processing Staff Excel data {"total_rows":1,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"<PERSON><PERSON><PERSON>"} 
[2025-07-31 09:34:01] local.INFO: Staff bulk import completed {"import_id":1,"processed_rows":1,"skipped_rows":0,"provider_id":"3","provider_name":"<PERSON>ashish Rawat"} 
[2025-07-31 09:34:01] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:04:01 +0630","user_id":1} 
[2025-07-31 09:34:04] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:04:03 +0630","user_id":1} 
[2025-07-31 09:34:12] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:04:12 +0630","user_id":1} 
[2025-07-31 09:34:22] local.INFO: 5 consecutive blank rows detected - stopping processing and discarding blank rows {"row_number":12,"consecutive_blank_rows":5} 
[2025-07-31 09:34:22] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:04:22 +0630","user_id":1} 
[2025-07-31 09:40:50] local.INFO: Processing Staff Excel data {"total_rows":6,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"Aashish Rawat"} 
[2025-07-31 09:40:50] local.INFO: Staff bulk import completed {"import_id":2,"processed_rows":6,"skipped_rows":0,"provider_id":"3","provider_name":"Aashish Rawat"} 
[2025-07-31 09:40:51] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:10:51 +0630","user_id":1} 
[2025-07-31 09:40:54] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:10:53 +0630","user_id":1} 
[2025-07-31 09:41:46] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:11:46 +0630","user_id":3} 
[2025-07-31 09:41:49] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:11:48 +0630","user_id":3} 
[2025-07-31 09:41:57] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:11:56 +0630","user_id":3} 
[2025-07-31 09:41:57] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-07-31 15:11:56 +0630","error":"Trailing data"} 
[2025-07-31 09:41:57] local.INFO: Using device time from session for signed_at {"timestamp":"2025-07-31 15:11:56 +0630"} 
[2025-07-31 09:41:57] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":2,"signed_at_db":"2025-07-31 15:11:56","formatted_signed_at":"07/31/2025 03:11 PM"} 
[2025-07-31 09:41:57] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":3,"signed_at_db":"2025-07-31 15:11:56","formatted_signed_at":"07/31/2025 03:11 PM"} 
[2025-07-31 09:41:57] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":4,"signed_at_db":"2025-07-31 15:11:56","formatted_signed_at":"07/31/2025 03:11 PM"} 
[2025-07-31 09:41:57] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"Aashish Rawat","count":3} 
[2025-07-31 09:41:58] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:11:58 +0630","user_id":3} 
[2025-07-31 09:42:01] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:12:01 +0630","user_id":1} 
[2025-07-31 09:42:09] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:12:09 +0630","user_id":3} 
[2025-07-31 09:42:13] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:12:12 +0630","user_id":1} 
[2025-07-31 09:42:15] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:12:15 +0630","user_id":1} 
[2025-07-31 09:43:39] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:13:39 +0630","user_id":1} 
[2025-07-31 09:43:43] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:13:43 +0630","user_id":3} 
[2025-07-31 09:44:12] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:14:12 +0630","user_id":3} 
[2025-07-31 09:44:12] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-07-31 15:14:12 +0630","error":"Trailing data"} 
[2025-07-31 09:44:12] local.INFO: Using device time from session for signed_at {"timestamp":"2025-07-31 15:14:12 +0630"} 
[2025-07-31 09:44:12] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":5,"signed_at_db":"2025-07-31 15:14:12","formatted_signed_at":"07/31/2025 03:14 PM"} 
[2025-07-31 09:44:12] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"Aashish Rawat","count":1} 
[2025-07-31 09:44:13] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:14:13 +0630","user_id":3} 
[2025-07-31 09:45:02] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:15:02 +0630","user_id":1} 
[2025-07-31 09:48:08] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:18:08 +0630","user_id":1} 
[2025-07-31 09:48:12] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:18:12 +0630","user_id":1} 
[2025-07-31 09:48:30] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-31 09:48:40] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:18:40 +0630","user_id":3} 
[2025-07-31 09:48:44] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:18:44 +0630","user_id":3} 
[2025-07-31 09:48:51] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:18:51 +0630","user_id":1} 
[2025-07-31 09:48:54] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:18:53 +0630","user_id":1} 
[2025-07-31 09:49:03] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:19:02 +0630","user_id":1} 
[2025-07-31 09:49:04] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:19:04 +0630","user_id":1} 
[2025-07-31 09:49:07] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:19:06 +0630","user_id":1} 
[2025-07-31 09:49:25] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:19:25 +0630","user_id":1} 
[2025-07-31 09:49:31] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:19:31 +0630","user_id":1} 
[2025-07-31 09:50:46] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:20:46 +0630","user_id":1} 
[2025-07-31 09:50:59] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-31 09:51:05] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:21:05 +0630","user_id":1} 
[2025-07-31 09:51:20] local.INFO: sendFaxAll request parameters {"all_params":{"_token":"1m6kD4CPYbhumLTLXg88ZK7xZmMGPrSXHsUBGn8K","status":"Pending Approval","changed_status":"Sent","displayed_ids":["2","3","4","5"]},"provider_id":null,"has_provider_id":false,"medication_id":null,"signed_date":null,"displayed_ids":["2","3","4","5"]} 
[2025-07-31 09:51:20] local.WARNING: No provider filter applied {"has_provider_id":false,"provider_id_value":null} 
[2025-07-31 09:51:20] local.INFO: Final query before execution {"sql":"select * from `import_files` where `status` = ?","bindings":["Pending Approval"]} 
[2025-07-31 09:51:20] local.INFO: Filtering displayed_ids {"original_count":4,"filtered_count":4,"original_ids":["2","3","4","5"],"filtered_ids":["2","3","4","5"]} 
[2025-07-31 09:51:20] local.INFO: Using filtered displayed_ids {"count":4,"results_count":4} 
[2025-07-31 09:51:20] local.INFO: Sending IDs to SendFilesToFaxJob {"import_file_fax":[],"import_file_dispense":[2,3,4,5],"count":4} 
[2025-07-31 09:51:20] local.WARNING: No import file IDs to process  
[2025-07-31 09:51:21] local.INFO: SendDispenseJob dispatched successfully in chunks {"user_id":1} 
[2025-07-31 09:51:21] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:21:21 +0630","user_id":1} 
[2025-07-31 09:51:42] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 2  
[2025-07-31 09:51:42] local.INFO: Set status to PENDING_APPROVAL for ImportFile ID: 2  
[2025-07-31 09:51:42] local.ERROR: Error processing ImportFile ID 2: Attempt to read property "name" on null  
[2025-07-31 09:51:42] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 3  
[2025-07-31 09:51:42] local.INFO: Set status to PENDING_APPROVAL for ImportFile ID: 3  
[2025-07-31 09:51:42] local.ERROR: Error processing ImportFile ID 3: Attempt to read property "name" on null  
[2025-07-31 09:51:42] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 4  
[2025-07-31 09:51:42] local.INFO: Set status to PENDING_APPROVAL for ImportFile ID: 4  
[2025-07-31 09:51:42] local.ERROR: Error processing ImportFile ID 4: Attempt to read property "name" on null  
[2025-07-31 09:51:42] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 5  
[2025-07-31 09:51:42] local.INFO: Set status to PENDING_APPROVAL for ImportFile ID: 5  
[2025-07-31 09:51:42] local.ERROR: Error processing ImportFile ID 5: Attempt to read property "name" on null  
[2025-07-31 09:53:08] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:23:07 +0630","user_id":1} 
[2025-07-31 09:53:09] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:23:09 +0630","user_id":1} 
[2025-07-31 09:53:18] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:23:18 +0630","user_id":3} 
[2025-07-31 09:53:25] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:23:24 +0630","user_id":1} 
[2025-07-31 09:53:35] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:23:34 +0630","user_id":1} 
[2025-07-31 09:53:38] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:23:37 +0630","user_id":1} 
[2025-07-31 09:54:08] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:24:08 +0630","user_id":1} 
[2025-07-31 09:54:18] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:24:18 +0630","user_id":1} 
[2025-07-31 09:54:47] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:24:47 +0630","user_id":1} 
[2025-07-31 09:54:52] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:24:51 +0630","user_id":1} 
[2025-07-31 09:54:57] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:24:56 +0630","user_id":1} 
[2025-07-31 09:55:59] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:25:58 +0630","user_id":1} 
[2025-07-31 09:56:23] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:26:23 +0630","user_id":1} 
[2025-07-31 09:56:30] local.INFO: sendFaxAll request parameters {"all_params":{"_token":"1m6kD4CPYbhumLTLXg88ZK7xZmMGPrSXHsUBGn8K","status":"Pending Approval","changed_status":"Sent","displayed_ids":["2","3","4","5"]},"provider_id":null,"has_provider_id":false,"medication_id":null,"signed_date":null,"displayed_ids":["2","3","4","5"]} 
[2025-07-31 09:56:30] local.WARNING: No provider filter applied {"has_provider_id":false,"provider_id_value":null} 
[2025-07-31 09:56:30] local.INFO: Final query before execution {"sql":"select * from `import_files` where `status` = ?","bindings":["Pending Approval"]} 
[2025-07-31 09:56:30] local.INFO: Filtering displayed_ids {"original_count":4,"filtered_count":4,"original_ids":["2","3","4","5"],"filtered_ids":["2","3","4","5"]} 
[2025-07-31 09:56:30] local.INFO: Using filtered displayed_ids {"count":4,"results_count":4} 
[2025-07-31 09:56:30] local.INFO: Sending IDs to SendFilesToFaxJob {"import_file_fax":[],"import_file_dispense":[2,3,4,5],"count":4} 
[2025-07-31 09:56:30] local.WARNING: No import file IDs to process  
[2025-07-31 09:56:30] local.INFO: SendDispenseJob dispatched successfully in chunks {"user_id":1} 
[2025-07-31 09:56:31] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:26:31 +0630","user_id":1} 
[2025-07-31 09:56:31] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 2  
[2025-07-31 09:56:31] local.INFO: Set status to PENDING_APPROVAL for ImportFile ID: 2  
[2025-07-31 09:56:31] local.ERROR: Error processing ImportFile ID 2: Attempt to read property "name" on null  
[2025-07-31 09:56:31] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 3  
[2025-07-31 09:56:31] local.INFO: Set status to PENDING_APPROVAL for ImportFile ID: 3  
[2025-07-31 09:56:31] local.ERROR: Error processing ImportFile ID 3: Attempt to read property "name" on null  
[2025-07-31 09:56:31] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 4  
[2025-07-31 09:56:31] local.INFO: Set status to PENDING_APPROVAL for ImportFile ID: 4  
[2025-07-31 09:56:31] local.ERROR: Error processing ImportFile ID 4: Attempt to read property "name" on null  
[2025-07-31 09:56:31] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 5  
[2025-07-31 09:56:31] local.INFO: Set status to PENDING_APPROVAL for ImportFile ID: 5  
[2025-07-31 09:56:31] local.ERROR: Error processing ImportFile ID 5: Attempt to read property "name" on null  
[2025-07-31 09:56:50] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:26:49 +0630","user_id":1} 
[2025-07-31 09:56:51] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:26:51 +0630","user_id":1} 
[2025-07-31 09:57:08] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:27:07 +0630","user_id":1} 
[2025-07-31 09:57:09] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:27:09 +0630","user_id":1} 
[2025-07-31 09:57:19] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:27:18 +0630","user_id":1} 
[2025-07-31 09:58:01] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:28:00 +0630","user_id":1} 
[2025-07-31 09:58:03] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:28:03 +0630","user_id":1} 
[2025-07-31 09:58:14] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:28:14 +0630","user_id":1} 
[2025-07-31 09:58:19] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:28:18 +0630","user_id":1} 
[2025-07-31 09:58:22] local.INFO: sendFaxAll request parameters {"all_params":{"_token":"1m6kD4CPYbhumLTLXg88ZK7xZmMGPrSXHsUBGn8K","status":"Pending Approval","changed_status":"Sent","displayed_ids":["2","3","4","5"]},"provider_id":null,"has_provider_id":false,"medication_id":null,"signed_date":null,"displayed_ids":["2","3","4","5"]} 
[2025-07-31 09:58:22] local.WARNING: No provider filter applied {"has_provider_id":false,"provider_id_value":null} 
[2025-07-31 09:58:22] local.INFO: Final query before execution {"sql":"select * from `import_files` where `status` = ?","bindings":["Pending Approval"]} 
[2025-07-31 09:58:22] local.INFO: Filtering displayed_ids {"original_count":4,"filtered_count":4,"original_ids":["2","3","4","5"],"filtered_ids":["2","3","4","5"]} 
[2025-07-31 09:58:22] local.INFO: Using filtered displayed_ids {"count":4,"results_count":4} 
[2025-07-31 09:58:22] local.INFO: Sending IDs to SendFilesToFaxJob {"import_file_fax":[],"import_file_dispense":[2,3,4,5],"count":4} 
[2025-07-31 09:58:22] local.WARNING: No import file IDs to process  
[2025-07-31 09:58:22] local.INFO: SendDispenseJob dispatched successfully in chunks {"user_id":1} 
[2025-07-31 09:58:22] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:28:22 +0630","user_id":1} 
[2025-07-31 09:58:29] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 2  
[2025-07-31 09:58:29] local.INFO: Set status to PENDING_APPROVAL for ImportFile ID: 2  
[2025-07-31 09:58:29] local.ERROR: Error processing ImportFile ID 2: Attempt to read property "name" on null  
[2025-07-31 09:58:29] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 3  
[2025-07-31 09:58:29] local.INFO: Set status to PENDING_APPROVAL for ImportFile ID: 3  
[2025-07-31 09:58:29] local.ERROR: Error processing ImportFile ID 3: Attempt to read property "name" on null  
[2025-07-31 09:58:29] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 4  
[2025-07-31 09:58:29] local.INFO: Set status to PENDING_APPROVAL for ImportFile ID: 4  
[2025-07-31 09:58:29] local.ERROR: Error processing ImportFile ID 4: Attempt to read property "name" on null  
[2025-07-31 09:58:29] local.INFO: SEND FILE TO DISPENSE PRO WITH ID 5  
[2025-07-31 09:58:29] local.INFO: Set status to PENDING_APPROVAL for ImportFile ID: 5  
[2025-07-31 09:58:29] local.ERROR: Error processing ImportFile ID 5: Attempt to read property "name" on null  
[2025-07-31 09:58:43] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:28:42 +0630","user_id":1} 
[2025-07-31 10:00:09] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:30:09 +0630","user_id":1} 
[2025-07-31 10:00:12] local.INFO: User signature path {"user_id":3,"signature_path":"signatures/6osRPSSeZNafiJbbsq5R79KQuQ3ejcEaKq0ZjJE5.png","exists":true,"full_path":"C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\storage\\app/public/signatures/6osRPSSeZNafiJbbsq5R79KQuQ3ejcEaKq0ZjJE5.png"} 
[2025-07-31 10:00:16] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:30:15 +0630","user_id":1} 
[2025-07-31 10:00:28] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:30:28 +0630","user_id":1} 
[2025-07-31 10:00:33] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:30:33 +0630","user_id":1} 
[2025-07-31 10:00:37] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Controllers\\ArchiveController.php:868)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\PROJECTS\\\\Max...', 868)
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Controllers\\ArchiveController.php(868): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\PROJECTS\\\\Max...', 868)
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ArchiveController->sendFax('2', Object(Illuminate\\Http\\Request))
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('sendFax', Array)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ArchiveController), 'sendFax')
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#57 {main}
"} 
[2025-07-31 10:00:48] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:30:48 +0630","user_id":1} 
[2025-07-31 10:00:52] local.INFO: User signature path {"user_id":3,"signature_path":"signatures/6osRPSSeZNafiJbbsq5R79KQuQ3ejcEaKq0ZjJE5.png","exists":true,"full_path":"C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\storage\\app/public/signatures/6osRPSSeZNafiJbbsq5R79KQuQ3ejcEaKq0ZjJE5.png"} 
[2025-07-31 10:00:53] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:30:52 +0630","user_id":1} 
[2025-07-31 10:01:23] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:31:22 +0630","user_id":1} 
[2025-07-31 10:01:25] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:31:25 +0630","user_id":1} 
[2025-07-31 10:01:27] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Controllers\\ArchiveController.php:868)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\PROJECTS\\\\Max...', 868)
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Controllers\\ArchiveController.php(868): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\PROJECTS\\\\Max...', 868)
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ArchiveController->sendFax('2', Object(Illuminate\\Http\\Request))
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('sendFax', Array)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ArchiveController), 'sendFax')
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#57 {main}
"} 
[2025-07-31 10:02:59] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:32:58 +0630","user_id":1} 
[2025-07-31 10:03:01] local.INFO: User signature path {"user_id":3,"signature_path":"signatures/6osRPSSeZNafiJbbsq5R79KQuQ3ejcEaKq0ZjJE5.png","exists":true,"full_path":"C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\storage\\app/public/signatures/6osRPSSeZNafiJbbsq5R79KQuQ3ejcEaKq0ZjJE5.png"} 
[2025-07-31 10:03:02] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:33:01 +0630","user_id":1} 
[2025-07-31 10:03:11] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:33:11 +0630","user_id":1} 
[2025-07-31 10:03:12] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:33:12 +0630","user_id":1} 
[2025-07-31 10:04:19] local.INFO: DispensePro order sent successfully for ImportFile ID: 2  
[2025-07-31 10:04:20] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:34:20 +0630","user_id":1} 
[2025-07-31 10:04:24] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Controllers\\ArchiveController.php:871)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\PROJECTS\\\\Max...', 871)
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Controllers\\ArchiveController.php(871): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\PROJECTS\\\\Max...', 871)
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ArchiveController->sendFax('3', Object(Illuminate\\Http\\Request))
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('sendFax', Array)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ArchiveController), 'sendFax')
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#57 {main}
"} 
[2025-07-31 10:11:51] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:41:51 +0630","user_id":1} 
[2025-07-31 10:11:53] local.INFO: User signature path {"user_id":3,"signature_path":"signatures/6osRPSSeZNafiJbbsq5R79KQuQ3ejcEaKq0ZjJE5.png","exists":true,"full_path":"C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\storage\\app/public/signatures/6osRPSSeZNafiJbbsq5R79KQuQ3ejcEaKq0ZjJE5.png"} 
[2025-07-31 10:11:53] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:41:53 +0630","user_id":1} 
[2025-07-31 10:12:01] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:42:00 +0630","user_id":1} 
[2025-07-31 10:12:09] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:42:08 +0630","user_id":1} 
[2025-07-31 10:13:19] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-31 10:14:09] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:44:08 +0630","user_id":1} 
[2025-07-31 10:14:27] local.INFO: DispensePro order sent successfully for ImportFile ID: 3  
[2025-07-31 10:14:27] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:44:27 +0630","user_id":1} 
[2025-07-31 10:14:30] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:44:30 +0630","user_id":1} 
[2025-07-31 10:14:47] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:44:47 +0630","user_id":1} 
[2025-07-31 10:16:03] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-31 10:16:51] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-31 10:18:42] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:48:42 +0630","user_id":1} 
[2025-07-31 10:18:57] local.INFO: Admin or operator void request {"user_id":1,"status":"Sent","requested_order_ids":["4WB10737"],"filtered_order_ids":["4WB10737"],"count":1} 
[2025-07-31 10:18:57] local.INFO: Sending IDs to VoidScriptJob {"order_ids":["4WB10737"],"count":1,"user_role":"administrator"} 
[2025-07-31 10:18:57] local.INFO: Void job has been queued in chunks. {"order_ids":["4WB10737"],"count":1} 
[2025-07-31 10:19:00] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:49:00 +0630","user_id":1} 
[2025-07-31 10:19:04] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:49:04 +0630","user_id":1} 
[2025-07-31 10:19:08] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:49:08 +0630","user_id":1} 
[2025-07-31 10:19:09] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:49:09 +0630","user_id":1} 
[2025-07-31 10:19:24] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:49:23 +0630","user_id":1} 
[2025-07-31 10:19:25] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:49:24 +0630","user_id":1} 
[2025-07-31 10:19:37] local.INFO: VoidScriptJob results [{"orderId":"4WB10737","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"z7fFLWTDSs","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"RAWAT","firstName":"RAHUL","middleName":"","address":"224 DUNN HARBOR","address2":"STE 950","city":"TIMOTHYFORT","region":"Connecticut","postal":"55124","countryCode":"US","homePhone":"","cellPhone":"(*************","workPhone":"","email":"","dob":"4/7/2024","gender":"m","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}}] 
[2025-07-31 10:19:37] local.INFO: VoidScriptJob completed {"voided_order_ids":["4WB10737"],"failed_order_ids":[],"results":[{"orderId":"4WB10737","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"z7fFLWTDSs","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"RAWAT","firstName":"RAHUL","middleName":"","address":"224 DUNN HARBOR","address2":"STE 950","city":"TIMOTHYFORT","region":"Connecticut","postal":"55124","countryCode":"US","homePhone":"","cellPhone":"(*************","workPhone":"","email":"","dob":"4/7/2024","gender":"m","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}}],"message":"1 script(s) voided, 0 failed.","user_id":1} 
[2025-07-31 10:19:50] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:49:49 +0630","user_id":1} 
[2025-07-31 10:20:43] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-31 10:20:55] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:50:55 +0630","user_id":1} 
[2025-07-31 10:23:43] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-31 10:25:33] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-31 10:28:08] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:58:08 +0630","user_id":1} 
[2025-07-31 10:28:14] local.INFO: Device time stored in session {"device_time":"2025-07-31 15:58:13 +0630","user_id":1} 
[2025-07-31 10:30:55] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:00:55 +0630","user_id":1} 
[2025-07-31 10:32:13] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:02:13 +0630","user_id":1} 
[2025-07-31 10:35:15] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:05:14 +0630","user_id":1} 
[2025-07-31 10:35:27] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:05:27 +0630","user_id":1} 
[2025-07-31 10:40:41] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:10:40 +0630","user_id":1} 
[2025-07-31 10:40:52] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:10:52 +0630","user_id":1} 
[2025-07-31 10:40:57] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:10:56 +0630","user_id":1} 
[2025-07-31 10:44:25] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:14:25 +0630","user_id":1} 
[2025-07-31 10:44:44] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:14:44 +0630","user_id":1} 
[2025-07-31 10:45:17] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:15:16 +0630","user_id":1} 
[2025-07-31 10:46:37] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:16:37 +0630","user_id":1} 
[2025-07-31 10:48:17] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:18:17 +0630","user_id":1} 
[2025-07-31 10:49:15] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-31 10:50:36] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:20:36 +0630","user_id":1} 
[2025-07-31 10:52:06] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-31 10:57:51] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:27:51 +0630","user_id":1} 
[2025-07-31 10:57:54] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:27:53 +0630","user_id":1} 
[2025-07-31 10:59:07] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-31 11:07:59] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-31 11:08:21] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-31 11:12:15] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:42:15 +0630","user_id":1} 
[2025-07-31 11:12:26] local.INFO: User signature path {"user_id":3,"signature_path":"signatures/6osRPSSeZNafiJbbsq5R79KQuQ3ejcEaKq0ZjJE5.png","exists":true,"full_path":"C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\storage\\app/public/signatures/6osRPSSeZNafiJbbsq5R79KQuQ3ejcEaKq0ZjJE5.png"} 
[2025-07-31 11:12:28] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:42:28 +0630","user_id":1} 
[2025-07-31 11:12:30] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:42:29 +0630","user_id":1} 
[2025-07-31 11:12:31] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:42:31 +0630","user_id":1} 
[2025-07-31 11:12:34] local.INFO: Device time stored in session {"device_time":"2025-07-31 16:42:33 +0630","user_id":1} 
[2025-07-31 11:21:28] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-07-31 11:55:01] local.INFO: Device time stored in session {"device_time":"2025-07-31 17:25:01 +0630","user_id":1} 
[2025-07-31 12:03:46] local.INFO: Device time stored in session {"device_time":"2025-07-31 17:33:46 +0630","user_id":1} 
[2025-07-31 12:08:51] local.INFO: Device time stored in session {"device_time":"2025-07-31 17:38:50 +0630","user_id":1} 
[2025-07-31 12:08:52] local.INFO: Device time stored in session {"device_time":"2025-07-31 17:38:52 +0630","user_id":1} 
[2025-07-31 12:09:28] local.INFO: Device time stored in session {"device_time":"2025-07-31 17:39:28 +0630","user_id":1} 
[2025-07-31 12:14:14] local.INFO: Device time stored in session {"device_time":"2025-07-31 17:44:13 +0630","user_id":1} 
[2025-07-31 12:43:44] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-31 12:43:48] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-31 12:43:54] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-01 04:33:26] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-01 04:33:29] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-01 04:33:34] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-01 04:46:37] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-01 04:51:57] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:21:57 +0630","user_id":1} 
[2025-08-01 04:52:10] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:22:10 +0630","user_id":1} 
[2025-08-01 04:52:19] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:22:19 +0630","user_id":1} 
[2025-08-01 04:54:22] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:24:21 +0630","user_id":1} 
[2025-08-01 04:56:24] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:26:23 +0630","user_id":1} 
[2025-08-01 04:56:47] local.INFO: Script returned for revision and data after regeneration {"import_file_id":5,"file_name":"prescription_4_TEST_Mccor.pdf","file_path":"public/prescriptions/2/prescription_4_TEST_Mccor.pdf","reason":"asdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdasdassssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss"} 
[2025-08-01 04:57:19] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-01 04:57:30] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:27:30 +0630","user_id":1} 
[2025-08-01 05:01:20] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-01 05:01:57] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:31:57 +0630","user_id":1} 
[2025-08-01 05:05:45] local.ERROR: syntax error, unexpected identifier "Route" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected identifier \"Route\" at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\routes\\web.php:155)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\PROJECTS\\\\Max...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('C:\\\\PROJECTS\\\\Max...')
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'C:\\\\PROJECTS\\\\Max...')
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Providers\\RouteServiceProvider.php(94): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\PROJECTS\\\\Max...')
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Providers\\RouteServiceProvider.php(63): App\\Providers\\RouteServiceProvider->mapWebRoutes()
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\RouteServiceProvider->map()
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(122): Illuminate\\Container\\Container->call(Array)
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1062): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#20 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 29)
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 {main}
"} 
[2025-08-01 05:05:47] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-01 05:06:37] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:36:36 +0630","user_id":1} 
[2025-08-01 05:06:55] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:36:55 +0630","user_id":3} 
[2025-08-01 05:06:59] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:36:59 +0630","user_id":3} 
[2025-08-01 05:07:03] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:37:02 +0630","user_id":3} 
[2025-08-01 05:10:21] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:40:20 +0630","user_id":3} 
[2025-08-01 05:10:23] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:40:23 +0630","user_id":3} 
[2025-08-01 05:10:43] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:40:42 +0630","user_id":3} 
[2025-08-01 05:21:53] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:51:53 +0630","user_id":3} 
[2025-08-01 05:23:35] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:53:34 +0630","user_id":3} 
[2025-08-01 05:23:36] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:53:36 +0630","user_id":3} 
[2025-08-01 05:23:38] local.INFO: Device time stored in session {"device_time":"2025-08-01 10:53:38 +0630","user_id":3} 
[2025-08-01 05:31:15] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:01:15 +0630","user_id":3} 
[2025-08-01 05:33:11] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:03:11 +0630","user_id":3} 
[2025-08-01 05:36:01] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:06:00 +0630","user_id":3} 
[2025-08-01 05:36:20] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:06:19 +0630","user_id":1} 
[2025-08-01 05:36:21] local.INFO: User signature path {"user_id":3,"signature_path":"signatures/6osRPSSeZNafiJbbsq5R79KQuQ3ejcEaKq0ZjJE5.png","exists":true,"full_path":"C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\storage\\app/public/signatures/6osRPSSeZNafiJbbsq5R79KQuQ3ejcEaKq0ZjJE5.png"} 
[2025-08-01 05:36:21] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:06:21 +0630","user_id":1} 
[2025-08-01 05:36:23] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:06:22 +0630","user_id":1} 
[2025-08-01 05:39:26] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:09:26 +0630","user_id":1} 
[2025-08-01 05:39:28] local.INFO: User signature path {"user_id":3,"signature_path":"signatures/6osRPSSeZNafiJbbsq5R79KQuQ3ejcEaKq0ZjJE5.png","exists":true,"full_path":"C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\storage\\app/public/signatures/6osRPSSeZNafiJbbsq5R79KQuQ3ejcEaKq0ZjJE5.png"} 
[2025-08-01 05:39:29] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:09:28 +0630","user_id":1} 
[2025-08-01 05:39:30] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:09:30 +0630","user_id":1} 
[2025-08-01 05:39:33] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:09:33 +0630","user_id":3} 
[2025-08-01 05:40:01] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-01 05:41:13] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-01 05:41:17] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-01 05:41:21] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-01 05:41:28] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-01 05:58:54] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:28:53 +0630","user_id":1} 
[2025-08-01 05:59:15] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:29:15 +0630","user_id":1} 
[2025-08-01 06:02:57] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:32:57 +0630","user_id":3} 
[2025-08-01 06:03:00] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:33:00 +0630","user_id":3} 
[2025-08-01 06:03:05] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:33:05 +0630","user_id":3} 
[2025-08-01 06:03:07] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:33:06 +0630","user_id":3} 
[2025-08-01 06:03:10] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:33:09 +0630","user_id":3} 
[2025-08-01 06:03:12] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:33:11 +0630","user_id":3} 
[2025-08-01 06:03:15] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:33:15 +0630","user_id":3} 
[2025-08-01 06:03:46] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:33:46 +0630","user_id":3} 
[2025-08-01 06:15:42] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-01 06:17:31] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:47:31 +0630","user_id":1} 
[2025-08-01 06:17:32] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:47:32 +0630","user_id":1} 
[2025-08-01 06:17:35] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:47:35 +0630","user_id":1} 
[2025-08-01 06:17:37] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Controllers\\ArchiveController.php:868)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\PROJECTS\\\\Max...', 868)
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Controllers\\ArchiveController.php(868): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\PROJECTS\\\\Max...', 868)
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ArchiveController->sendFax('4', Object(Illuminate\\Http\\Request))
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('sendFax', Array)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ArchiveController), 'sendFax')
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#57 {main}
"} 
[2025-08-01 06:18:06] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:48:06 +0630","user_id":1} 
[2025-08-01 06:18:24] local.INFO: DispensePro order sent successfully for ImportFile ID: 4  
[2025-08-01 06:18:25] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:48:25 +0630","user_id":1} 
[2025-08-01 06:18:48] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:48:47 +0630","user_id":1} 
[2025-08-01 06:21:02] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:51:01 +0630","user_id":1} 
[2025-08-01 06:21:21] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:51:20 +0630","user_id":1} 
[2025-08-01 06:22:07] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:52:07 +0630","user_id":1} 
[2025-08-01 06:22:25] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:52:24 +0630","user_id":1} 
[2025-08-01 06:22:37] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:52:36 +0630","user_id":1} 
[2025-08-01 06:22:45] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:52:45 +0630","user_id":1} 
[2025-08-01 06:22:57] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:52:56 +0630","user_id":1} 
[2025-08-01 06:23:02] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:53:02 +0630","user_id":1} 
[2025-08-01 06:23:11] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:53:11 +0630","user_id":1} 
[2025-08-01 06:23:42] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:53:41 +0630","user_id":1} 
[2025-08-01 06:23:53] local.INFO: Device time stored in session {"device_time":"2025-08-01 11:53:53 +0630","user_id":1} 
[2025-08-01 06:26:50] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-01 06:50:38] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-01 06:50:50] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:20:50 +0630","user_id":1} 
[2025-08-01 06:50:56] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:20:56 +0630","user_id":1} 
[2025-08-01 06:51:06] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:21:05 +0630","user_id":1} 
[2025-08-01 06:51:09] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:21:08 +0630","user_id":1} 
[2025-08-01 06:51:23] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:21:22 +0630","user_id":3} 
[2025-08-01 06:51:26] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:21:25 +0630","user_id":3} 
[2025-08-01 06:51:29] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-08-01 12:21:28 +0630","error":"Trailing data"} 
[2025-08-01 06:51:29] local.INFO: Using device time from session for signed_at {"timestamp":"2025-08-01 12:21:25 +0630"} 
[2025-08-01 06:51:29] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":6,"signed_at_db":"2025-08-01 12:21:25","formatted_signed_at":"08/01/2025 12:21 PM"} 
[2025-08-01 06:51:31] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"Aashish Rawat","count":1} 
[2025-08-01 06:51:32] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:21:32 +0630","user_id":3} 
[2025-08-01 06:51:33] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:21:32 +0630","user_id":1} 
[2025-08-01 06:51:34] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Controllers\\ArchiveController.php:868)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\PROJECTS\\\\Max...', 868)
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Controllers\\ArchiveController.php(868): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\PROJECTS\\\\Max...', 868)
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ArchiveController->sendFax('6', Object(Illuminate\\Http\\Request))
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('sendFax', Array)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ArchiveController), 'sendFax')
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#57 {main}
"} 
[2025-08-01 06:51:50] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:21:49 +0630","user_id":1} 
[2025-08-01 06:52:08] local.INFO: DispensePro order sent successfully for ImportFile ID: 6  
[2025-08-01 06:52:08] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:22:08 +0630","user_id":1} 
[2025-08-01 06:52:14] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:22:13 +0630","user_id":1} 
[2025-08-01 06:52:23] local.ERROR: Command "queue:worj" is not defined.

Did you mean one of these?
    queue:batches-table
    queue:clear
    queue:failed
    queue:failed-table
    queue:flush
    queue:forget
    queue:listen
    queue:monitor
    queue:prune-batches
    queue:prune-failed
    queue:restart
    queue:retry
    queue:retry-batch
    queue:table
    queue:work
    schedule:work {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"queue:worj\" is not defined.

Did you mean one of these?
    queue:batches-table
    queue:clear
    queue:failed
    queue:failed-table
    queue:flush
    queue:forget
    queue:listen
    queue:monitor
    queue:prune-batches
    queue:prune-failed
    queue:restart
    queue:retry
    queue:retry-batch
    queue:table
    queue:work
    schedule:work at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('queue:worj')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-08-01 06:52:31] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:22:30 +0630","user_id":3} 
[2025-08-01 06:52:40] local.INFO: Filtering by medication {"medication_id":"2","medication_name":"Tirzepatide"} 
[2025-08-01 06:52:57] local.INFO: Provider void request {"user_id":3,"status":"Sent","requested_order_ids":null,"filtered_order_ids":["K8Y99053","********","NYF56252"],"count":3} 
[2025-08-01 06:52:57] local.INFO: Sending IDs to VoidScriptJob {"order_ids":["K8Y99053","********","NYF56252"],"count":3,"user_role":"provider"} 
[2025-08-01 06:52:57] local.INFO: Void job has been queued in chunks. {"order_ids":["K8Y99053","********","NYF56252"],"count":3} 
[2025-08-01 06:52:58] local.INFO: Filtering by medication {"medication_id":"2","medication_name":"Tirzepatide"} 
[2025-08-01 06:53:41] local.INFO: VoidScriptJob results [{"orderId":"K8Y99053","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"edsL3P7zR9","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"SHARMA","firstName":"YOGESH","middleName":"","address":"224 BHOOT COLONY","address2":"","city":"WEST JAMESVIEW","region":"Ohio","postal":"43120","countryCode":"US","homePhone":"","cellPhone":"(*************","workPhone":"","email":"","dob":"4/8/2024","gender":"m","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}},{"orderId":"********","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"TEcmr1x6U6","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"SANTOKI","firstName":"SATVIK","middleName":"","address":"84301 CODY ST","address2":"STE 716","city":"MICHAELBOROUGH","region":"Alaska","postal":"21813","countryCode":"US","homePhone":"","cellPhone":"(*************","workPhone":"","email":"","dob":"2/1/2005","gender":"f","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}},{"orderId":"NYF56252","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"7J6hNgQpVW","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"SHAH","firstName":"SHREYAL","middleName":"","address":"12 TARASLI","address2":"","city":"VADODARA","region":"California","postal":"55412","countryCode":"US","homePhone":"","cellPhone":"(*************","workPhone":"","email":"","dob":"1/2/2001","gender":"m","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}}] 
[2025-08-01 06:53:41] local.INFO: VoidScriptJob completed {"voided_order_ids":["K8Y99053","********","NYF56252"],"failed_order_ids":[],"results":[{"orderId":"K8Y99053","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"edsL3P7zR9","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"SHARMA","firstName":"YOGESH","middleName":"","address":"224 BHOOT COLONY","address2":"","city":"WEST JAMESVIEW","region":"Ohio","postal":"43120","countryCode":"US","homePhone":"","cellPhone":"(*************","workPhone":"","email":"","dob":"4/8/2024","gender":"m","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}},{"orderId":"********","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"TEcmr1x6U6","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"SANTOKI","firstName":"SATVIK","middleName":"","address":"84301 CODY ST","address2":"STE 716","city":"MICHAELBOROUGH","region":"Alaska","postal":"21813","countryCode":"US","homePhone":"","cellPhone":"(*************","workPhone":"","email":"","dob":"2/1/2005","gender":"f","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}},{"orderId":"NYF56252","status":200,"response":{"apiKey":"maxcoretest","action":"void","version":1,"transactionId":"7J6hNgQpVW","warningMessages":[],"errorMessages":[],"status":"ok","pharmacy":{"name":"DIRX","address":"3540 Nw 56th St","address2":"STE 204","city":"Ft Lauderdale","region":"Florida","postal":"33309","countryCode":"US","phone":"(*************","fax":"(*************","ncpdpId":"5707801","dea":"*********","npi":"**********"},"patient":{"externalId":"","groupName":"","origin":"","lastName":"SHAH","firstName":"SHREYAL","middleName":"","address":"12 TARASLI","address2":"","city":"VADODARA","region":"California","postal":"55412","countryCode":"US","homePhone":"","cellPhone":"(*************","workPhone":"","email":"","dob":"1/2/2001","gender":"m","race":"","ethnicity":"","language":"","ssn":"","notes":"","wellnessId":"","drugAllerges":""},"orders":[]}}],"message":"3 script(s) voided, 0 failed.","user_id":3} 
[2025-08-01 06:55:35] local.INFO: Filtering by medication {"medication_id":"1","medication_name":"Semaglutide"} 
[2025-08-01 06:55:45] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:25:45 +0630","user_id":3} 
[2025-08-01 06:55:49] local.INFO: Filtering by medication {"medication_id":"1","medication_name":"Semaglutide"} 
[2025-08-01 06:56:07] local.INFO: Filtering by medication {"medication_id":"1","medication_name":"Semaglutide"} 
[2025-08-01 06:56:37] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:26:36 +0630","user_id":3} 
[2025-08-01 06:57:34] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:27:34 +0630","user_id":3} 
[2025-08-01 06:57:36] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:27:36 +0630","user_id":3} 
[2025-08-01 07:00:53] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-01 07:03:28] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:33:28 +0630","user_id":3} 
[2025-08-01 07:03:38] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:33:37 +0630","user_id":3} 
[2025-08-01 07:04:46] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:34:46 +0630","user_id":1} 
[2025-08-01 07:05:48] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:35:47 +0630","user_id":1} 
[2025-08-01 07:05:50] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:35:50 +0630","user_id":1} 
[2025-08-01 07:05:52] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:35:51 +0630","user_id":1} 
[2025-08-01 07:05:55] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:35:55 +0630","user_id":1} 
[2025-08-01 07:08:47] local.ERROR: Too many arguments to "make:multistep-form" command, expected arguments "name" "steps". {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): Too many arguments to \"make:multistep-form\" command, expected arguments \"name\" \"steps\". at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php:196)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(89): Symfony\\Component\\Console\\Input\\ArgvInput->parseArgument('select-provider...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('select-provider...', true)
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\GenerateMultiStepForm), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 {main}
"} 
[2025-08-01 07:08:55] local.ERROR: Too many arguments to "make:multistep-form" command, expected arguments "name" "steps". {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): Too many arguments to \"make:multistep-form\" command, expected arguments \"name\" \"steps\". at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php:196)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(89): Symfony\\Component\\Console\\Input\\ArgvInput->parseArgument('select-provider...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('select-provider...', true)
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\GenerateMultiStepForm), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 {main}
"} 
[2025-08-01 07:09:26] local.ERROR: Too many arguments to "make:multistep-form" command, expected arguments "name" "steps". {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): Too many arguments to \"make:multistep-form\" command, expected arguments \"name\" \"steps\". at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php:196)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(89): Symfony\\Component\\Console\\Input\\ArgvInput->parseArgument(':')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken(':', true)
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\GenerateMultiStepForm), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 {main}
"} 
[2025-08-01 07:27:50] local.INFO: Device time stored in session {"device_time":"2025-08-01 12:57:50 +0630","user_id":1} 
[2025-08-01 07:33:50] local.ERROR: Not enough arguments (missing: "name"). {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): Not enough arguments (missing: \"name\"). at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Input\\Input.php:77)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(321): Symfony\\Component\\Console\\Input\\Input->validate()
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\GenerateMultiStepForm), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 {main}
"} 
[2025-08-01 07:35:21] local.INFO: Device time stored in session {"device_time":"2025-08-01 13:05:21 +0630","user_id":1} 
[2025-08-01 07:35:22] local.INFO: Device time stored in session {"device_time":"2025-08-01 13:05:22 +0630","user_id":1} 
[2025-08-01 07:41:16] local.ERROR: Class "App\Http\Controllers\Api\App\Http\Controllers\TestController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\App\\Http\\Controllers\\TestController\" does not exist at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:225)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(225): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(147): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(116): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 11)
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(600): array_map(Object(Closure), Array, Array)
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(778): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(115): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(101): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"} 
[2025-08-01 07:42:56] local.INFO: Device time stored in session {"device_time":"2025-08-01 13:12:55 +0630","user_id":1} 
[2025-08-01 07:43:19] local.ERROR: Unable to find component: [patient-form.patient-form] {"userId":1,"exception":"[object] (Livewire\\Exceptions\\ComponentNotFoundException(code: 0): Unable to find component: [patient-form.patient-form] at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php:79)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(88): Livewire\\LivewireManager->getClass('patient-form.pa...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(29): Livewire\\LivewireManager->getInstance('patient-form.pa...', 'Vfin9HMrWWkhYCt...')
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Livewire\\LifecycleManager::Livewire\\{closure}(Object(Livewire\\LifecycleManager))
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(28): tap(Object(Livewire\\LifecycleManager), Object(Closure))
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(108): Livewire\\LifecycleManager::fromInitialRequest('patient-form.pa...', 'Vfin9HMrWWkhYCt...')
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Livewire\\LivewireManager->mount('patient-form.pa...')
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\storage\\framework\\views\\b365a877cfdee360a2c9f5cd5b310d55.php(28): Illuminate\\Support\\Facades\\Facade::__callStatic('mount', Array)
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\PROJECTS\\\\Max...')
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\PROJECTS\\\\Max...', Array)
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#71 {main}
"} 
[2025-08-01 07:43:41] local.ERROR: Unable to find component: [patient-form.patient-form] {"userId":1,"exception":"[object] (Livewire\\Exceptions\\ComponentNotFoundException(code: 0): Unable to find component: [patient-form.patient-form] at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php:79)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(88): Livewire\\LivewireManager->getClass('patient-form.pa...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(29): Livewire\\LivewireManager->getInstance('patient-form.pa...', 'ru54ZSjJthPxuyM...')
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Livewire\\LifecycleManager::Livewire\\{closure}(Object(Livewire\\LifecycleManager))
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(28): tap(Object(Livewire\\LifecycleManager), Object(Closure))
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(108): Livewire\\LifecycleManager::fromInitialRequest('patient-form.pa...', 'ru54ZSjJthPxuyM...')
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Livewire\\LivewireManager->mount('patient-form.pa...')
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\storage\\framework\\views\\b365a877cfdee360a2c9f5cd5b310d55.php(28): Illuminate\\Support\\Facades\\Facade::__callStatic('mount', Array)
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\PROJECTS\\\\Max...')
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\PROJECTS\\\\Max...', Array)
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#71 {main}
"} 
[2025-08-01 07:43:48] local.ERROR: Unable to find component: [patient-form.patient-form] {"userId":1,"exception":"[object] (Livewire\\Exceptions\\ComponentNotFoundException(code: 0): Unable to find component: [patient-form.patient-form] at C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php:79)
[stacktrace]
#0 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(88): Livewire\\LivewireManager->getClass('patient-form.pa...')
#1 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(29): Livewire\\LivewireManager->getInstance('patient-form.pa...', 'chXLLX3dJjte5D8...')
#2 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Livewire\\LifecycleManager::Livewire\\{closure}(Object(Livewire\\LifecycleManager))
#3 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(28): tap(Object(Livewire\\LifecycleManager), Object(Closure))
#4 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(108): Livewire\\LifecycleManager::fromInitialRequest('patient-form.pa...', 'chXLLX3dJjte5D8...')
#5 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Livewire\\LivewireManager->mount('patient-form.pa...')
#6 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\storage\\framework\\views\\b365a877cfdee360a2c9f5cd5b310d55.php(28): Illuminate\\Support\\Facades\\Facade::__callStatic('mount', Array)
#7 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\PROJECTS\\\\Max...')
#8 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#9 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\PROJECTS\\\\Max...', Array)
#10 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#11 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\PROJECTS\\\\Max...', Array)
#12 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#13 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\PROJECTS\\\\Max...', Array)
#14 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#15 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#16 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#17 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#18 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#19 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#20 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\SetLocale.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\PROJECTS\\Max-life\\rx-maxlife-panel\\server.php(21): require_once('C:\\\\PROJECTS\\\\Max...')
#71 {main}
"} 
