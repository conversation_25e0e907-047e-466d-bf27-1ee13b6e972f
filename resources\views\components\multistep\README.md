# Multi-Step Form Components

This directory contains reusable Blade components for creating multi-step forms in Laravel Livewire applications.

## Components

### 1. `form-wrapper.blade.php`
A complete wrapper component that includes both step indicator and navigation buttons.

**Props:**
- `steps` (array) - Array of step labels indexed by step number
- `currentStep` (int) - Current active step number
- `totalSteps` (int) - Total number of steps
- `editMode` (bool) - Whether form is in edit mode (allows direct step navigation)
- `gap` (string) - CSS gap between step indicators (default: '4rem')
- `showStepIndicator` (bool) - Show/hide step progress indicator (default: true)
- `showNavigation` (bool) - Show/hide navigation buttons (default: true)
- `prevButtonText` (string) - Previous button text (default: 'Previous')
- `nextButtonText` (string) - Next button text (default: 'Next')
- `submitButtonText` (string) - Submit button text (default: 'Create'/'Update' based on editMode)
- `prevButtonClass` (string) - CSS classes for previous button (default: 'btn btn-secondary')
- `nextButtonClass` (string) - CSS classes for next button (default: 'btn btn-primary')
- `submitButtonClass` (string) - CSS classes for submit button (default: 'btn btn-success')

**Usage:**
```blade
<x-multistep.form-wrapper 
    :steps="$steps" 
    :currentStep="$step" 
    :totalSteps="$totalSteps"
    :editMode="$editMode"
>
    <!-- Your step content goes here -->
    @if($step === 1)
        @include('your-form.step1')
    @elseif($step === 2)
        @include('your-form.step2')
    @endif
</x-multistep.form-wrapper>
```

### 2. `step-indicator.blade.php`
A standalone step progress indicator component.

**Props:**
- `steps` (array) - Array of step labels indexed by step number
- `currentStep` (int) - Current active step number
- `editMode` (bool) - Whether form is in edit mode (allows clicking steps)
- `gap` (string) - CSS gap between step indicators (default: '4rem')

**Usage:**
```blade
<x-multistep.step-indicator 
    :steps="$steps" 
    :currentStep="$step" 
    :editMode="$editMode" 
/>
```

### 3. `navigation.blade.php`
A standalone navigation buttons component.

**Props:**
- `currentStep` (int) - Current active step number
- `totalSteps` (int) - Total number of steps
- `editMode` (bool) - Whether form is in edit mode
- `prevButtonText` (string) - Previous button text (default: 'Previous')
- `nextButtonText` (string) - Next button text (default: 'Next')
- `submitButtonText` (string) - Submit button text (default: 'Create'/'Update' based on editMode)
- `prevButtonClass` (string) - CSS classes for previous button (default: 'btn btn-secondary')
- `nextButtonClass` (string) - CSS classes for next button (default: 'btn btn-primary')
- `submitButtonClass` (string) - CSS classes for submit button (default: 'btn btn-success')
- `showInCard` (bool) - Wrap in card footer (default: true)

**Usage:**
```blade
<x-multistep.navigation 
    :currentStep="$step" 
    :totalSteps="$totalSteps" 
    :editMode="$editMode" 
/>
```

## Livewire Component Requirements

Your Livewire component should have these methods for the components to work:

```php
public function nextStep()
{
    $this->validateCurrentStep();
    if ($this->step < $this->totalSteps) {
        $this->step++;
    }
}

public function prevStep()
{
    if ($this->step > 1) {
        $this->step--;
    }
}

public function goToStep($stepNumber)
{
    // Only allow direct step navigation in edit mode
    if ($this->editMode && $stepNumber >= 1 && $stepNumber <= $this->totalSteps) {
        $this->step = $stepNumber;
    }
}

public function submit()
{
    // Your form submission logic
}
```

## Generated by Command

These components are automatically used when generating multi-step forms with:

```bash
php artisan make:multistep-form FormName 3 --step-labels="Step 1,Step 2,Step 3"
```

## Customization Examples

### Custom Button Styling
```blade
<x-multistep.form-wrapper 
    :steps="$steps" 
    :currentStep="$step" 
    :totalSteps="$totalSteps"
    :editMode="$editMode"
    nextButtonClass="btn btn-success"
    submitButtonClass="btn btn-warning"
    prevButtonText="Go Back"
    nextButtonText="Continue"
>
    <!-- content -->
</x-multistep.form-wrapper>
```

### Hide Step Indicator
```blade
<x-multistep.form-wrapper 
    :steps="$steps" 
    :currentStep="$step" 
    :totalSteps="$totalSteps"
    :editMode="$editMode"
    :showStepIndicator="false"
>
    <!-- content -->
</x-multistep.form-wrapper>
```

### Custom Layout
```blade
<div class="my-custom-container">
    <x-multistep.step-indicator :steps="$steps" :currentStep="$step" :editMode="$editMode" />
    
    <div class="my-content-area">
        <!-- Your step content -->
    </div>
    
    <x-multistep.navigation 
        :currentStep="$step" 
        :totalSteps="$totalSteps" 
        :editMode="$editMode"
        :showInCard="false"
    />
</div>
```
