<div>
    <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Email','labelRequired' => '1','model' => 'email','type' => 'email','placeholder' => 'Enter email address']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Email','labelRequired' => '1','model' => 'email','type' => 'email','placeholder' => 'Enter email address']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
    <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'First Name','labelRequired' => '1','model' => 'first_name','placeholder' => 'Enter first name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'First Name','labelRequired' => '1','model' => 'first_name','placeholder' => 'Enter first name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
    <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Last Name','labelRequired' => '1','model' => 'last_name','placeholder' => 'Enter last name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Last Name','labelRequired' => '1','model' => 'last_name','placeholder' => 'Enter last name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
    <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Printed Name','labelRequired' => '1','model' => 'printed_name','placeholder' => 'Enter printed name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Printed Name','labelRequired' => '1','model' => 'printed_name','placeholder' => 'Enter printed name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
    <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Clinic Name','labelRequired' => '1','model' => 'clinic_name','placeholder' => 'Enter clinic name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Clinic Name','labelRequired' => '1','model' => 'clinic_name','placeholder' => 'Enter clinic name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>

</div>
<?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/livewire/provider/provider-steps/step1.blade.php ENDPATH**/ ?>